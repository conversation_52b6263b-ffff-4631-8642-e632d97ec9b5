name: Build and Package Cura Installer (Windows)

on:
  workflow_dispatch:
    inputs:
      cura_version:
        description: 'Cura Version'
        default: '5.11.0-alpha.0'
        required: true
        type: string
      enterprise:
        description: 'Build Enterprise Edition'
        default: false
        required: false
        type: boolean
      staging:
        description: 'Use Staging API'
        default: false
        required: false
        type: boolean

jobs:
  build-windows:
    runs-on: windows-2022

    env:
      CURA_VERSION: ${{ inputs.cura_version }}
      CONAN_USER_HOME: ${{ github.workspace }}\.conan2

    steps:
    - name: 🧾 检出 Cura 源码
      uses: actions/checkout@v4
      with:
        fetch-depth: 1

    - name: 🐍 安装 Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
        cache: 'pip'

    - name: 🛠 设置 MSVC 环境
      uses: microsoft/setup-msbuild@v1.3

    - name: 🛠 设置 Visual Studio 环境
      uses: ilammy/msvc-dev-cmd@v1
      with:
        arch: x64

    - name: 🧪 安装 Python 依赖
      run: |
        python -m pip install --upgrade pip
        pip install conan==2.7.0
        pip install sip==6.5.1
        pip install gitpython 

    - name: 📦 缓存 Conan 包
      uses: actions/cache@v3
      with:
        path: ~/.conan2
        key: conan-${{ runner.os }}-${{ hashFiles('**/conanfile.py', '**/conandata.yml') }}
        restore-keys: |
          conan-${{ runner.os }}-

    - name: ⚙️ 配置 Conan
      run: |
        conan config install https://github.com/wsd07/conan-config.git
        conan profile detect --force

    - name: 🔍 验证 Conan 配置
      run: |
        conan profile show
        conan remote list

    - name: 🔃 克隆依赖仓库
      run: |
        git clone https://github.com/wsd07/Uranium.git ../Uranium
        git clone https://github.com/wsd07/CuraEngine.git ../CuraEngine

    - name: 🔗 设置 editable 依赖
      run: |
        conan editable add ../Uranium --name=uranium --version=5.11.0 --user=wsd07 --channel=testing
        conan editable add ../CuraEngine --name=curaengine --version=5.11.0 --user=wsd07 --channel=testing

    - name: 🏗 创建 Conan 虚拟环境并生成 PyInstaller spec
      run: |
        # 使用官方文档推荐的方法创建虚拟环境
        # 在 Conan 2.x 中，deploy() 方法会在 conan install 时自动调用生成 PyInstaller spec
        Write-Host "运行 conan install 创建虚拟环境并生成 PyInstaller spec 文件..."

        # 启用详细输出以便调试
        try {
          conan install . --build=missing --update -g VirtualPythonEnv --deployer-folder=. -v
        } catch {
          Write-Host "❌ conan install 执行失败: $($_.Exception.Message)"
          Write-Host "尝试查看conan日志..."
          if (Test-Path "$env:CONAN_USER_HOME\.conan2\logs") {
            Get-ChildItem "$env:CONAN_USER_HOME\.conan2\logs" -Name "*.log" | ForEach-Object {
              Write-Host "=== $_ ==="
              Get-Content "$env:CONAN_USER_HOME\.conan2\logs\$_" | Select-Object -Last 50
            }
          }
          throw
        }

    - name: ✅ 验证虚拟环境和spec文件创建
      run: |
        # 检查虚拟环境脚本 (Windows使用.bat文件)
        if (Test-Path "build\generators\virtual_python_env.bat") {
          Write-Host "✅ 虚拟环境脚本已创建"
        } else {
          Write-Host "❌ 虚拟环境脚本未找到"
          Write-Host "build/generators 目录内容："
          if (Test-Path "build\generators") {
            Get-ChildItem "build\generators" | ForEach-Object { Write-Host "  - $($_.Name)" }
          } else {
            Write-Host "build/generators 目录不存在"
          }
          exit 1
        }

        # 检查PyInstaller spec文件是否已生成
        Write-Host "查找所有.spec文件："
        $specFiles = Get-ChildItem -Name "*.spec"
        if ($specFiles) {
          foreach ($file in $specFiles) {
            Write-Host "  找到spec文件: $file"
          }

          # 检查标准的UltiMaker-Cura.spec文件
          if (Test-Path "UltiMaker-Cura.spec") {
            Write-Host "✅ 标准PyInstaller spec文件已生成"
            $specFile = "UltiMaker-Cura.spec"
          } else {
            # 使用找到的第一个spec文件
            $specFile = $specFiles[0]
            Write-Host "⚠️ 使用找到的spec文件: $specFile"
          }
        } else {
          Write-Host "❌ 没有找到任何PyInstaller spec文件"
          Write-Host "尝试手动运行deploy方法..."

          # 尝试手动运行conan create来触发deploy
          try {
            Write-Host "运行 conan create 来强制执行deploy方法..."
            conan create . --build=missing -tf=""
          } catch {
            Write-Host "conan create 也失败了，检查错误..."
          }

          # 再次检查spec文件
          $specFiles = Get-ChildItem -Name "*.spec"
          if ($specFiles) {
            $specFile = $specFiles[0]
            Write-Host "✅ 通过conan create生成了spec文件: $specFile"
          } else {
            Write-Host "当前目录内容："
            Get-ChildItem | ForEach-Object { Write-Host "  - $($_.Name)" }

            # 检查是否有.jinja模板文件
            if (Test-Path "UltiMaker-Cura.spec.jinja") {
              Write-Host "✅ 找到Jinja模板文件，但未渲染成spec文件"
              Write-Host "这可能是因为deploy()方法中的错误"
            }
            exit 1
          }
        }

        # 将spec文件名保存到环境变量中供后续步骤使用
        echo "PYINSTALLER_SPEC_FILE=$specFile" >> $env:GITHUB_ENV

    - name: 🔨 安装 NSIS
      run: |
        choco install nsis -y
        $nsisPath = "${env:ProgramFiles(x86)}\NSIS"
        echo "$nsisPath" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

    - name: 🔨 使用 PyInstaller 构建可执行文件
      run: |
        # 激活 Conan 创建的虚拟环境 (Windows使用.bat文件)
        & "build\generators\virtual_python_env.bat"

        # 安装 PyInstaller
        python -m pip install --upgrade pip
        pip install pyinstaller==6.3.0

        # 使用之前检测到的spec文件
        $specFile = $env:PYINSTALLER_SPEC_FILE
        Write-Host "使用PyInstaller spec文件: $specFile"

        if (Test-Path $specFile) {
          Write-Host "✅ PyInstaller spec 文件存在，准备构建"

          # 显示spec文件的前几行以确认内容
          Write-Host "Spec文件内容预览："
          Get-Content $specFile | Select-Object -First 5 | ForEach-Object { Write-Host "  $_" }
        } else {
          Write-Host "❌ PyInstaller spec 文件不存在: $specFile"
          exit 1
        }

        # 运行 PyInstaller
        Write-Host "开始运行 PyInstaller..."
        pyinstaller $specFile --clean --noconfirm --log-level=INFO

        # 验证可执行文件生成 - 动态检测可执行文件名
        Write-Host "检查dist目录中的可执行文件..."
        if (Test-Path "dist") {
          $exeFiles = Get-ChildItem -Recurse "dist" -Name "*.exe"
          if ($exeFiles) {
            Write-Host "找到可执行文件："
            foreach ($exe in $exeFiles) {
              $fullPath = Join-Path "dist" $exe
              $exe_size = (Get-Item $fullPath).Length/1MB
              Write-Host "  - $exe (大小: $([math]::Round($exe_size, 2)) MB)"
            }
            Write-Host "✅ 可执行文件生成成功"
          } else {
            Write-Host "❌ PyInstaller 未生成可执行文件"
            Write-Host "dist 目录内容："
            Get-ChildItem -Recurse "dist" | Select-Object -First 20 | ForEach-Object { Write-Host "  - $($_.FullName)" }
            exit 1
          }
        } else {
          Write-Host "❌ dist 目录不存在"

          # 检查PyInstaller的工作目录
          if (Test-Path "build") {
            Write-Host "build 目录内容："
            Get-ChildItem -Recurse "build" | Select-Object -First 10 | ForEach-Object { Write-Host "  - $($_.FullName)" }
          }
          exit 1
        }

    - name: 📦 创建 Windows 安装程序
      run: |
        # 激活虚拟环境
        & "build\generators\virtual_python_env.bat"

        # 安装必要的依赖
        pip install jinja2 semver

        # 检查 NSIS 脚本
        $nsis_script = "packaging\NSIS\create_windows_installer.py"
        if (Test-Path $nsis_script) {
          Write-Host "✅ NSIS 脚本存在: $nsis_script"
        } else {
          Write-Host "❌ NSIS 脚本不存在: $nsis_script"
          Write-Host "packaging 目录内容："
          if (Test-Path "packaging") {
            Get-ChildItem -Recurse "packaging" | Select-Object -First 10 | ForEach-Object { Write-Host "  - $($_.FullName)" }
          }
          exit 1
        }

        # 检查 PyInstaller 输出
        if (Test-Path "dist\UltiMaker-Cura") {
          Write-Host "✅ PyInstaller 输出目录存在"
        } else {
          Write-Host "❌ PyInstaller 输出目录不存在"
          exit 1
        }

        # 生成安装程序文件名
        $installer_filename = "UltiMaker-Cura-${{ env.CURA_VERSION }}-Windows-x64.exe"
        Write-Host "生成安装程序: $installer_filename"

        # 执行 NSIS 脚本
        python $nsis_script --source_path . --dist_path dist --filename $installer_filename --version ${{ env.CURA_VERSION }}

        # 检查安装程序是否生成
        if (Test-Path "dist\$installer_filename") {
          $size = (Get-Item "dist\$installer_filename").Length/1MB
          Write-Host "✅ 安装程序生成成功 (大小: $size MB)"
        } else {
          Write-Host "❌ 安装程序未生成"
          Write-Host "dist 目录内容："
          Get-ChildItem "dist" | ForEach-Object { Write-Host "  - $($_.Name)" }
          exit 1
        }

    - name: ☁️ 上传安装包
      uses: actions/upload-artifact@v4
      with:
        name: cura-windows-installer-${{ env.CURA_VERSION }}
        path: dist/UltiMaker-Cura-${{ env.CURA_VERSION }}-Windows-x64.exe
        retention-days: 7
