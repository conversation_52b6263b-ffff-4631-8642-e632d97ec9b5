# -*- mode: python ; coding: utf-8 -*-
# PyInstaller spec file for local Cura build

import os
from pathlib import Path
from PyInstaller.utils.hooks import collect_all

block_cipher = None

datas = [
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/resources', 'share/cura/resources'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/plugins', 'share/cura/plugins'),
    ('/Users/<USER>/Desktop/Cura-Dev/Uranium/resources', 'share/uranium/resources'),
    ('/Users/<USER>/Desktop/Cura-Dev/Uranium/plugins', 'share/uranium/plugins'),
    ('/Users/<USER>/Desktop/Cura-Dev/Uranium/UM/Qt/qml/UM', 'PyQt6/Qt6/qml/UM'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/LICENSE', 'share/licenses'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/packaging/cura_license.txt', 'share/licenses'),
    ('/Users/<USER>/Desktop/Cura-Dev/Uranium/LICENSE', 'share/licenses'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/cura/CuraVersion.py', 'share/version'),
    ('/Users/<USER>/Desktop/Cura-Dev/Uranium/UM/Version.py', 'share/version')
]
binaries = [
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/CuraEngine', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/CuraEngine', 'bin'),
    ('/Users/<USER>/.conan2/p/b/pyarc5072b2430e630/p/lib/pyArcus.so', '.'),
    ('/Users/<USER>/.conan2/p/b/arcus0af90e54d845b/p/lib/libArcus.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/dulci41971a88cd1f1/p/lib/libpython3.12.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/pysavb115827a241dc/p/lib/pySavitar.so', '.'),
    ('/Users/<USER>/.conan2/p/b/savit95f4934232802/p/lib/libSavitar.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/pynes95985d9cfd775/p/lib/pynest2d.so', '.'),
    ('/Users/<USER>/.conan2/p/b/nest2bde2bc1e80b04/p/lib/libnest2d.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/nlopt3e392069e66fa/p/lib/libnlopt.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/nlopt3e392069e66fa/p/lib/libnlopt.0.11.1.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/nlopt3e392069e66fa/p/lib/libnlopt.0.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/clippe5637b009d224/p/lib/libpolyclipping.22.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/clippe5637b009d224/p/lib/libpolyclipping.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/clippe5637b009d224/p/lib/libpolyclipping.22.0.0.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/cpythcebf3dceed553/p/lib/libpython3.12.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/pyarc5072b2430e630/p/lib/pyArcus.so', '.'),
    ('/Users/<USER>/.conan2/p/b/arcus0af90e54d845b/p/lib/libArcus.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/dulci41971a88cd1f1/p/lib/libpython3.12.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/pysavb115827a241dc/p/lib/pySavitar.so', '.'),
    ('/Users/<USER>/.conan2/p/b/savit95f4934232802/p/lib/libSavitar.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/pynes95985d9cfd775/p/lib/pynest2d.so', '.'),
    ('/Users/<USER>/.conan2/p/b/nest2bde2bc1e80b04/p/lib/libnest2d.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/nlopt3e392069e66fa/p/lib/libnlopt.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/nlopt3e392069e66fa/p/lib/libnlopt.0.11.1.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/nlopt3e392069e66fa/p/lib/libnlopt.0.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/clippe5637b009d224/p/lib/libpolyclipping.22.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/clippe5637b009d224/p/lib/libpolyclipping.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/clippe5637b009d224/p/lib/libpolyclipping.22.0.0.dylib', '.'),
    ('/Users/<USER>/.conan2/p/b/cpythcebf3dceed553/p/lib/libpython3.12.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/libpython3.12.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/libpolyclipping.22.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/libnest2d.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/libArcus.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/libnlopt.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/libnlopt.0.11.1.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/libSavitar.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/libnlopt.0.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/libpolyclipping.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/legacy.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/fips.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/venv/lib/libpolyclipping.22.0.0.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtWebSockets/libqmlwebsocketsplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQml/libqmlmetaplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick3D/libqquick3dplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtRemoteObjects/libdeclarative_remoteobjectsplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtSensors/libsensorsquickplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/libqtquick2plugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtWebChannel/libwebchannelquickplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtMultimedia/libquickmultimediaplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtPositioning/libpositioningquickplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtTextToSpeech/libtexttospeechqmlplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtTest/libquicktestplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQml/Models/libmodelsplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQml/WorkerScript/libworkerscriptplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQml/XmlListModel/libqmlxmllistmodelplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQml/Base/libqmlplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick3D/ParticleEffects/libqtquick3dparticleeffectsplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick3D/AssetUtils/libqtquick3dassetutilsplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick3D/SpatialAudio/libquick3dspatialaudioplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick3D/Particles3D/libqtquick3dparticles3dplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick3D/Physics/libqquick3dphysicsplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick3D/Effects/libqtquick3deffectplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick3D/Helpers/libqtquick3dhelpersplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick3D/Physics/Helpers/libqtquick3dphysicshelpersplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick3D/Helpers/impl/libqtquick3dhelpersimplplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/tooling/libquicktoolingplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Scene2D/libqtquickscene2dplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Pdf/libpdfquickplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Shapes/libqmlshapesplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Particles/libparticlesplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/libqtquickcontrols2plugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Layouts/libqquicklayoutsplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Window/libquickwindowplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/LocalStorage/libqmllocalstorageplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Templates/libqtquicktemplates2plugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Dialogs/libqtquickdialogsplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Scene3D/libqtquickscene3dplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Effects/libeffectsplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Timeline/libqtquicktimelineplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/NativeStyle/libqtquickcontrols2nativestyleplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/Fusion/libqtquickcontrols2fusionstyleplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/impl/libqtquickcontrols2implplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/macOS/libqtquickcontrols2macosstyleplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/Universal/libqtquickcontrols2universalstyleplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/Basic/libqtquickcontrols2basicstyleplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/iOS/libqtquickcontrols2iosstyleplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/Material/libqtquickcontrols2materialstyleplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/Imagine/libqtquickcontrols2imaginestyleplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/Fusion/impl/libqtquickcontrols2fusionstyleimplplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/Universal/impl/libqtquickcontrols2universalstyleimplplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/Basic/impl/libqtquickcontrols2basicstyleimplplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/iOS/impl/libqtquickcontrols2iosstyleimplplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/Material/impl/libqtquickcontrols2materialstyleimplplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Controls/Imagine/impl/libqtquickcontrols2imaginestyleimplplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/qml/QtQuick/Dialogs/quickimpl/libqtquickdialogs2quickimplplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/renderers/librhirenderer.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/renderers/libopenglrenderer.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/sensors/libqtsensors_generic.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/sceneparsers/libgltfsceneexport.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/sceneparsers/libassimpsceneimport.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/sceneparsers/libgltfsceneimport.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqoffscreen.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqminimal.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/platforms/libqcocoa.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/assetimporters/libassimp.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/position/libqtposition_positionpoll.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/position/libqtposition_cl.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/position/libqtposition_nmea.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/webview/libqtwebview_webengine.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/webview/libqtwebview_darwin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/geometryloaders/libdefaultgeometryloader.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/geometryloaders/libgltfgeometryloader.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/styles/libqmacstyle.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/tls/libqsecuretransportbackend.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/tls/libqopensslbackend.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/tls/libqcertonlybackend.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/iconengines/libqsvgicon.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/sqldrivers/libqsqlite.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/sqldrivers/libqsqlodbc.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/sqldrivers/libqsqlpsql.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/multimedia/libffmpegmediaplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/multimedia/libdarwinmediaplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqgif.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqwbmp.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqwebp.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqico.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacheif.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqjpeg.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqtiff.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqsvg.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqpdf.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqicns.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqtga.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/imageformats/libqmacjp2.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/qmllint/libquicklintplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/texttospeech/libqtexttospeech_speechdarwin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/texttospeech/libqtexttospeech_mock.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/texttospeech/libqtexttospeech_speech_macos.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/generic/libqtuiotouchplugin.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/renderplugins/libscene2d.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/Qt6/plugins/networkinformation/libqscnetworkreachability.dylib', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtWebSockets.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtRemoteObjects.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtWidgets.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtHelp.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtPositioning.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtGui.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtTextToSpeech.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtOpenGL.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtPrintSupport.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtSerialPort.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtSql.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtQml.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtBluetooth.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/sip.cpython-312-darwin.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtSvg.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtSensors.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtDesigner.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtSvgWidgets.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtMultimediaWidgets.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtWebChannel.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtNetwork.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtCore.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtNfc.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtTest.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtQuick.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtPdfWidgets.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtDBus.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtSpatialAudio.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtOpenGLWidgets.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtPdf.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtMultimedia.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtQuickWidgets.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtQuick3D.abi3.so', '.'),
    ('/Users/<USER>/Desktop/Cura-Dev/Cura/build/generators/cura_venv/lib/python3.12/site-packages/PyQt6/QtXml.abi3.so', '.')
]
hiddenimports = [
    'pySavitar',
    'pyArcus',
    'pyDulcificum',
    'pynest2d',
    'cura',
    'UM',
    'PyQt6',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.QtQml',
    'PyQt6.QtQuick',
    'PyQt6.QtOpenGL',
    'PyQt6.QtNetwork',
    'PyQt6.sip',
    'logging.handlers',
    'zeroconf',
    'fcntl',
    'stl',
    'serial',
    'pkgutil',
    'numpy',
    'scipy',
    'trimesh',
    'shapely',
    'sqlite3',
    'Charon',
    'win32ctypes',
    'keyrings.alt'
]

# Collect all modules (like official conan build)
collect_all_modules = [
    'cura',
    'UM',
    'serial',
    'Charon',
    'sqlite3',
    'trimesh',
    'win32ctypes',
    'PyQt6.QtNetwork',
    'PyQt6.sip',
    'stl',
    'keyrings.alt'
]
for module in collect_all_modules:
    try:
        tmp_ret = collect_all(module)
        datas += tmp_ret[0]
        binaries += tmp_ret[1]
        hiddenimports += tmp_ret[2]
        print(f"✓ Collected all for module: {module}")
    except Exception as e:
        print(f"⚠️  Failed to collect all for {module}: {e}")

a = Analysis(
    ['/Users/<USER>/Desktop/Cura-Dev/Cura/cura_app.py'],
    pathex=['/Users/<USER>/Desktop/Cura-Dev/Cura', '/Users/<USER>/Desktop/Cura-Dev/Uranium'],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='UltiMaker-Cura',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=os.getenv('CODESIGN_IDENTITY', None),
    entitlements_file='/Users/<USER>/Desktop/Cura-Dev/Cura/packaging/MacOS/cura.entitlements' if Path('/Users/<USER>/Desktop/Cura-Dev/Cura/packaging/MacOS/cura.entitlements').exists() else None,
    icon='/Users/<USER>/Desktop/Cura-Dev/Cura/packaging/icons/Cura.icns' if Path('/Users/<USER>/Desktop/Cura-Dev/Cura/packaging/icons/Cura.icns').exists() else None,
    contents_directory='.'
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='UltiMaker-Cura'
)

app = BUNDLE(
    coll,
    name='UltiMaker-Cura.app',
    icon='/Users/<USER>/Desktop/Cura-Dev/Cura/packaging/icons/Cura.icns' if Path('/Users/<USER>/Desktop/Cura-Dev/Cura/packaging/icons/Cura.icns').exists() else None,
    bundle_identifier='nl.ultimaker.cura',
    info_plist={
        'CFBundleDisplayName': 'UltiMaker-Cura',
        'CFBundleVersion': '5.11.0-alpha.0',
        'CFBundleShortVersionString': '5.11.0-alpha.0',
        'NSHighResolutionCapable': True,
        'NSRequiresAquaSystemAppearance': False,
    }
)
