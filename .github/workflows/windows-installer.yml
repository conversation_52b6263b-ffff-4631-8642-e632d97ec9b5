name: Windows Installer Build

on:
  workflow_dispatch:
    inputs:
      cura_conan_version:
        description: 'Cura Conan Version'
        default: 'cura/5.11.0-alpha.0@_/_'
        required: true
        type: string
      conan_args:
        description: 'Conan args: eq.: --require-override'
        default: ''
        required: false
        type: string
      enterprise:
        description: 'Build Cura as an Enterprise edition'
        default: false
        required: true
        type: boolean
      staging:
        description: 'Use staging API'
        default: false
        required: true
        type: boolean
      installer:
        description: 'Create the installer'
        default: true
        required: true
        type: boolean

jobs:
  windows-installer:
    runs-on: windows-2022
    
    outputs:
      installer_filename: ${{ steps.filename.outputs.INSTALLER_FILENAME }}

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 1

    - name: Setup Python and pip
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
        cache: 'pip'

    - name: Setup MSVC
      uses: microsoft/setup-msbuild@v1.3

    - name: Setup Visual Studio environment
      uses: ilammy/msvc-dev-cmd@v1
      with:
        arch: x64

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install conan==2.7.0

    - name: Cache Conan packages
      uses: actions/cache@v3
      with:
        path: ~/.conan2
        key: conan-${{ runner.os }}-${{ hashFiles('**/conanfile.py', '**/conandata.yml') }}
        restore-keys: |
          conan-${{ runner.os }}-

    - name: Configure Conan
      run: |
        conan config install https://github.com/wsd07/conan-config.git -a "-b runner/${{ runner.os }}"
        conan profile detect --force

    - name: Verify Conan configuration
      run: |
        conan profile show default
        conan remote list

    - name: Create Python virtual environment for Conan
      run: |
        python -m venv cura_build_venv
        cura_build_venv\Scripts\activate.ps1
        python -m pip install --upgrade pip

    - name: Install Cura dependencies with Conan
      run: |
        cura_build_venv\Scripts\activate.ps1
        cd Cura
        conan install . --build=missing --update -g VirtualPythonEnv -c user.curaengine:devtools=True -o cura:enterprise=${{ inputs.enterprise }} -o cura:staging=${{ inputs.staging }} -o cura:internal=False ${{ inputs.conan_args }}

    - name: Verify virtual environment creation
      run: |
        if (Test-Path "Cura\build\generators\virtual_python_env.ps1") {
          Write-Host "✅ 虚拟环境脚本已创建"
        } else {
          Write-Host "❌ 虚拟环境脚本未找到"
          exit 1
        }

    - name: Check deploy folder structure
      run: |
        cd Cura
        if (Test-Path "build\generators\cura_venv") {
          Write-Host "✅ Cura虚拟环境目录存在"
          Get-ChildItem "build\generators\cura_venv" -Recurse | Select-Object -First 10
        } else {
          Write-Host "❌ Cura虚拟环境目录不存在"
        }

    - name: Activate Cura environment and build
      run: |
        cd Cura
        & "build\generators\virtual_python_env.ps1"
        python -m pip install --upgrade pip
        pip install pyinstaller
        
        # 检查PyInstaller spec文件
        if (Test-Path "UltiMaker-Cura.spec") {
          Write-Host "✅ PyInstaller spec文件存在"
        } else {
          Write-Host "❌ PyInstaller spec文件不存在"
          exit 1
        }
        
        # 运行PyInstaller
        pyinstaller UltiMaker-Cura.spec --clean --noconfirm

    - name: Install NSIS
      run: |
        # 下载并安装NSIS
        Invoke-WebRequest -Uri "https://downloads.sourceforge.net/project/nsis/NSIS%203/3.08/nsis-3.08-setup.exe" -OutFile "nsis-setup.exe"
        Start-Process -FilePath "nsis-setup.exe" -ArgumentList "/S" -Wait

        # 添加NSIS到PATH
        $env:PATH += ";C:\Program Files (x86)\NSIS"
        echo "C:\Program Files (x86)\NSIS" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

    - name: Create Windows installer
      run: |
        cd Cura
        & "build\generators\virtual_python_env.ps1"

        # 检查dist目录
        if (Test-Path "dist\UltiMaker-Cura") {
          Write-Host "✅ PyInstaller输出目录存在"
          Get-ChildItem "dist\UltiMaker-Cura" | Select-Object -First 10
        } else {
          Write-Host "❌ PyInstaller输出目录不存在"
          exit 1
        }

        # 获取版本信息
        $version = "${{ inputs.cura_conan_version }}" -replace "cura/", "" -replace "@.*", ""
        $filename = "UltiMaker-Cura-$version-Windows-X64.exe"

        Write-Host "创建安装程序: $filename"
        Write-Host "版本: $version"

        # 使用官方NSIS脚本创建安装程序
        python packaging\NSIS\create_windows_installer.py --source_path . --dist_path dist --filename $filename --version $version

    - name: Output installer filename
      id: filename
      shell: python
      run: |
        import os
        import glob
        
        # 查找安装程序文件
        os.chdir("Cura/dist")
        installers = glob.glob("*.exe")
        
        if installers:
            installer_filename = installers[0]
            print(f"找到安装程序: {installer_filename}")
            with open(os.environ['GITHUB_OUTPUT'], 'a') as f:
                f.write(f"INSTALLER_FILENAME={installer_filename}\n")
        else:
            print("❌ 未找到安装程序文件")
            # 列出当前目录内容进行调试
            print("当前目录内容:")
            for item in os.listdir("."):
                print(f"  {item}")
            exit 1

    - name: Upload installer
      if: ${{ always() && inputs.installer }}
      uses: actions/upload-artifact@v3
      with:
        name: ${{ steps.filename.outputs.INSTALLER_FILENAME }}
        path: |
          Cura/*.exe
        retention-days: 5

    - name: Create run info
      shell: python
      run: |
        import os
        
        cura_conan_version = "${{ inputs.cura_conan_version }}"
        enterprise = "${{ inputs.enterprise }}"
        staging = "${{ inputs.staging }}"
        
        with open("run_info.sh", "w") as f:
            f.write(f'echo "CURA_VERSION={cura_conan_version}" >> $GITHUB_OUTPUT\n')
            f.write(f'echo "ENTERPRISE={enterprise}" >> $GITHUB_OUTPUT\n')
            f.write(f'echo "STAGING={staging}" >> $GITHUB_OUTPUT\n')

    - name: Upload run info
      uses: actions/upload-artifact@v3
      with:
        name: windows-run-info
        path: run_info.sh
        retention-days: 5
